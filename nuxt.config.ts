import tailwindcss from '@tailwindcss/vite';
import oxlintPlugin from 'vite-plugin-oxlint';
export default defineNuxtConfig({
  css: ['./assets/css/tailwind.css'],
  vite: {
    plugins: [tailwindcss(), oxlintPlugin()],
  },
  modules: [
    '@nuxthub/core',
    'nuxt-mcp',
    'shadcn-nuxt',
    'motion-v/nuxt',
    '@vueuse/nuxt',
  ],
  shadcn: {
    /**
     * Prefix for all the imported component
     */
    prefix: '',
    /**
     * Directory that the component lives in.
     * @default "./components/ui"
     */
    componentDir: './app/components/ui',
  },
  devtools: {
    enabled: true,

    timeline: {
      enabled: true,
    },
  },

  runtimeConfig: {
    public: {
      helloText: 'Hello from the Edge 👋',
    },
  },
  future: { compatibilityVersion: 4 },
  compatibilityDate: '2025-03-01',
  hub: {},
});
