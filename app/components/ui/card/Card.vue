import type { HTMLAttributes } from 'vue';
import { cn } from '@/lib/utils';
import { useBrutalMotion } from '~/composables/useBrutalMotion';

interface Props {
  class?: HTMLAttributes['class'];
  animationType?: 'slide-left' | 'slide-right' | 'fade' | 'pop' | 'none';
  delay?: number;
  disableMotion?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  animationType: 'slide-left',
  delay: 0,
  disableMotion: false,
});

// Motion setup for brutal animations
const {
  brutalSlideLeft,
  brutalSlideRight,
  brutalFade,
  brutalPop,
  brutalHover,
  prefersReducedMotion,
} = useBrutalMotion();

const cardRef = ref<HTMLElement>();

// Apply motion only if not disabled and user doesn't prefer reduced motion
const shouldUseMotion = computed(
  () => !props.disableMotion && !prefersReducedMotion()
);

// Select animation variant based on type
const getAnimationVariant = () => {
  switch (props.animationType) {
    case 'slide-left':
      return brutalSlideLeft;
    case 'slide-right':
      return brutalSlideRight;
    case 'fade':
      return brutalFade;
    case 'pop':
      return brutalPop;
    default:
      return brutalSlideLeft;
  }
};

// Motion variants for card animations
const cardMotion = computed(() => {
  if (!shouldUseMotion.value || props.animationType === 'none') return {};

  const baseVariant = getAnimationVariant();

  return {
    initial: baseVariant.initial,
    enter: {
      ...baseVariant.enter,
      transition: {
        ...baseVariant.enter?.transition,
        delay: props.delay,
      },
    },
    hovered: brutalHover.hovered,
  };
});
