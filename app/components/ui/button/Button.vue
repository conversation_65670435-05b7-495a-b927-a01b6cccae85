import type { PrimitiveProps } from 'reka-ui';

import { Primitive } from 'reka-ui';

import type { HTMLAttributes } from 'vue';

import { useBrutalMotion } from '~/composables/useBrutalMotion';
import { cn } from '~/lib/utils';
import { type ButtonVariants, buttonVariants } from '.';

interface Props extends PrimitiveProps {
  variant?: ButtonVariants['variant'];
  size?: ButtonVariants['size'];
  class?: HTMLAttributes['class'];
  disableMotion?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  as: 'button',
  disableMotion: false,
});

// Motion setup for brutal animations
const { brutalPop, brutalTouch, prefersReducedMotion } = useBrutalMotion();
const buttonRef = ref<HTMLElement>();

// Apply motion only if not disabled and user doesn't prefer reduced motion
const shouldUseMotion = computed(
  () => !props.disableMotion && !prefersReducedMotion()
);

// Motion variants for button interactions
const buttonMotion = computed(() => {
  if (!shouldUseMotion.value) return {};

  return {
    initial: brutalPop.initial,
    enter: brutalPop.enter,
    tapped: brutalPop.tapped,
    hovered: {
      x: -2,
      y: -2,
      transition: {
        duration: 150,
        ease: 'linear',
      },
    },
  };
});
